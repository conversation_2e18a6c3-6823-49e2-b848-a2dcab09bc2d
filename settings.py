# settings.py

# 基本设置
app_path = '{{APP_PATH}}'  # 使用占位符
format = 'UDBZ'           # 压缩格式
size = '1g'              # 将 'auto' 改为具体大小，如 1GB
volname = 'Kwaipilot'       # DMG 卷标名
filename = '{{filename}}'  # 输出文件名

# 窗口设置
window_rect = ((100, 100), (600, 400))  # 窗口位置和大小 (x, y, width, height)
icon_size = 100                         # 图标大小

# 图标位置 - 使用 DMG 内的文件名
icon_locations = {
    'Kwaipilot.app': (150, 150),        # App 图标的位置 (x, y)
    'Applications': (450, 150)          # 应用程序文件夹快捷方式的位置
}


# 权限设置
permissions = {
    app_path: 0o755
}

# 要包含的文件和符号链接
files = [app_path]
symlinks = {
    'Applications': '/Applications'     # 创建指向 /Applications 的快捷方式
}

# 视图设置
default_view = 'icon-view'
show_status_bar = False
show_tab_view = False
show_toolbar = False
show_pathbar = False
show_sidebar = False
sidebar_width = 180

# 排列选项
arrange_by = None
grid_offset = (0, 0)
grid_spacing = 100
scroll_position = (0, 0)
label_pos = 'bottom'  # 标签位置: 'bottom' 或 'right'
text_size = 16
icon_size = 100
