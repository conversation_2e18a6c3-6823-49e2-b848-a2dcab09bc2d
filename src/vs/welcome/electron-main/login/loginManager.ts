import { BrowserWindow, shell } from 'electron';
import { getSSOUrl, getDeviceId, USER_INFO_KEY, getUserTokenInfo, LOGIN_STATUS_KEY, DEVICE_ID_KEY } from './loginUtils.js';

export interface UserInfo {
	name: string;
	ticket?: string;
	mail?: string;
	displayName?: string;
	avatar?: string;
	deviceId?: string;
}

export class LoginManager {
	private loginWindow: BrowserWindow | undefined;
	private pollInterval: NodeJS.Timeout | undefined;

	constructor(
		private readonly storageService: any
	) { }

	async show(): Promise<{ status: string; message: string; userInfo?: UserInfo }> {
		try {
			// 生成设备ID
			const deviceId = getDeviceId(this.storageService);

			// 获取SSO URL
			const ssoUrl = getSSOUrl(deviceId);

			// 直接打开 url
			await shell.openExternal(ssoUrl);

			// 返回Promise，在登录成功或窗口关闭时resolve
			return new Promise((resolve) => {
				this.startPolling(deviceId, resolve);

				// 监听窗口关闭事件
				this.loginWindow?.on('closed', () => {
					this.cleanup();
					resolve({
						status: 'cancelled',
						message: 'Login window closed'
					});
				});
			});
		} catch (error) {
			console.error('Failed to create login window:', error);
			throw error;
		}
	}

	private startPolling(deviceId: string, resolve: (value: any) => void) {
		this.pollInterval = setInterval(async () => {
			try {
				// 这里需要替换为实际的API调用
				const userInfo = await this.checkLoginStatus(deviceId);
				if (userInfo) {
					// 存储用户信息
					this.storeUserInfo(userInfo);
					this.cleanup();
					resolve({
						status: 'success',
						message: 'Login successful',
						userInfo
					});
				}
			} catch (error) {
				console.error('Login check failed:', error);
			}
		}, 2000);
	}

	private async checkLoginStatus(deviceId: string): Promise<UserInfo | null> {
		const userInfoStr = this.storageService.get(USER_INFO_KEY);
		try {
			if (userInfoStr) {
				return JSON.parse(userInfoStr);
			}
		} catch (err) {
			console.log(err);
		}
		const userInfo = await getUserTokenInfo(deviceId);
		return userInfo ?? null;
	}

	private storeUserInfo(userInfo: UserInfo): void {
		this.storageService.store(USER_INFO_KEY, JSON.stringify(userInfo));
		this.storageService.store(LOGIN_STATUS_KEY, true);
		if (userInfo.deviceId) {
			this.storageService.store(DEVICE_ID_KEY, userInfo.deviceId);
		}
	}

	private cleanup() {
		if (this.pollInterval) {
			clearInterval(this.pollInterval);
			this.pollInterval = undefined;
		}
		if (this.loginWindow) {
			this.loginWindow.close();
			this.loginWindow = undefined;
		}
	}
}
