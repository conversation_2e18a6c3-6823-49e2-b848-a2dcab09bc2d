import * as path from 'path';
import * as fs from 'original-fs';
import { BrowserWindow } from 'electron';
import { FileAccess } from '../../../../vs/base/common/network.js';

export class WelcomeWindow {
    private window: BrowserWindow | null = null;
    private watchers: fs.FSWatcher[] = [];

    constructor(private readonly isDev: boolean) { }

    async show(): Promise<BrowserWindow> {
        this.window = new BrowserWindow({
            width: 1440,
            height: 864,
            center: true,
            titleBarStyle: 'hidden',
            resizable: false,
            webPreferences: {
                nodeIntegration: false,
                contextIsolation: true,
                preload: FileAccess.asFileUri('vs/welcome/preload.js').fsPath,
                devTools: this.isDev,
            },
        });

        if (this.isDev) {
            this.window.webContents.openDevTools();
        }

        // 加载欢迎页面
        await this.window.loadFile(FileAccess.asFileUri('vs/welcome/index.html').fsPath);

        if (this.isDev) {
            this.setupHotReload();
        }

        return this.window;
    }

    private setupHotReload() {
        if (!this.window) return;

        const welcomePath = FileAccess.asFileUri('vs/welcome').fsPath;
        const watchDirs = [
            path.join(welcomePath, 'styles'),
            path.join(welcomePath, 'js'),
            welcomePath
        ];

        // 创建防抖的重载函数
        const debouncedReload = this.debounce(() => {
            if (this.window && !this.window.isDestroyed()) {
                this.window.reload();
            }
        }, 700);

        // 监听所有相关目录
        watchDirs.forEach(dir => {
            try {
                const watcher = fs.watch(dir, (eventType, filename) => {
                    if (filename) {
                        debouncedReload();
                    }
                });
                this.watchers.push(watcher);
            } catch (err) {
                console.error(`Failed to watch directory: ${dir}`, err);
            }
        });

        // 当窗口关闭时清理文件监听
        this.window.on('closed', () => {
            this.cleanup();
        });
    }

    private cleanup() {
        this.watchers.forEach(watcher => watcher.close());
        this.watchers = [];
    }

    private debounce(func: Function, wait: number) {
        let timeout: NodeJS.Timeout | null = null;
        return (...args: any[]) => {
            if (timeout) {
                clearTimeout(timeout);
            }
            timeout = setTimeout(() => {
                func.apply(this, args);
                timeout = null;
            }, wait);
        };
    }
}
