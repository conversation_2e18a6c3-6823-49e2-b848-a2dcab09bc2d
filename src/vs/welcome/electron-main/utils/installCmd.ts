import { realpath } from 'fs/promises';
import * as fs from 'fs';
import { Promises, SymlinkSupport } from '../../../../vs/base/node/pfs.js';
import { dialog, MessageBoxReturnValue } from 'electron';
import { CancellationError } from '../../../../vs/base/common/errors.js';
import { promisify } from 'util';
import { exec } from 'child_process';
import { dirname, resolve } from '../../../base/common/path.js';
import { FileAccess } from '../../../base/common/network.js';

// Error codes for shell command installation
export const enum InstallErrorCode {
	Success = 0,
	NotSupported = 1,
	AccessDenied = 2,
	AlreadyExists = 3,
	PathError = 4,
	Unknown = 999
}

export class InstallError extends Error {
	constructor(message: string, public readonly code: InstallErrorCode) {
		super(message);
	}
}

// src/vs/platform/native/electron-main/nativeHostMainService.ts:358
export class ShellCommandInstaller {
	constructor(private readonly applicationName: string) {
	}

	private get appRoot(): string { return dirname(FileAccess.asFileUri('').fsPath); }

	private async getShellCommandLink() {
		const target = resolve(this.appRoot, 'bin', 'code');
		const source = `/usr/local/bin/${this.applicationName}`;

		// Ensure source exists
		const sourceExists = await Promises.exists(target);
		if (!sourceExists) {
			throw new Error(`"Unable to find shell script in '${target}'"`);
		}

		return { source, target };
	}

	async installShellCommand(): Promise<void> {
		const { source, target } = await this.getShellCommandLink();

		try {
			const { symbolicLink } = await SymlinkSupport.stat(source);
			if (symbolicLink && !symbolicLink.dangling) {
				const linkTargetRealPath = await realpath(source);
				if (target === linkTargetRealPath) {
					return;
				}
			}

			// Different source, delete it first
			await fs.promises.unlink(source);
		} catch (error) {
			if (error.code !== 'ENOENT') {
				throw error; // throw on any error but file not found
			}
		}

		try {
			await fs.promises.symlink(target, source);
		} catch (error) {
			if (error.code !== 'EACCES' && error.code !== 'ENOENT') {
				throw error;
			}

			const { response }: MessageBoxReturnValue | undefined = await dialog.showMessageBox({
				type: 'info',
				message: `${this.applicationName} will now prompt with 'powershell' for Administrator privileges to install the shell command.`,
				buttons: [
					'OK',
					'Cancel'
				]
			});

			if (response === 1 /* Cancel */) {
				throw new CancellationError();
			}

			try {
				const command = `osascript -e "do shell script \\"mkdir -p /usr/local/bin && ln -sf \'${target}\' \'${source}\'\\" with administrator privileges"`;
				await promisify(exec)(command);
			} catch (error) {
				throw new Error(`Unable to install the shell command '${source}'.`);
			}
		}
	}

	async uninstallShellCommand(windowId: number | undefined): Promise<void> {
		const { source } = await this.getShellCommandLink();

		try {
			await fs.promises.unlink(source);
		} catch (error) {
			switch (error.code) {
				case 'EACCES': {
					const { response }: MessageBoxReturnValue | undefined = await dialog.showMessageBox({
						type: 'info',
						message: `${this.applicationName} will now prompt with 'osascript' for Administrator privileges to uninstall the shell command.`,
						buttons: [
							'OK',
							'Cancel'
						]
					});

					if (response === 1 /* Cancel */) {
						throw new CancellationError();
					}

					try {
						const command = `osascript -e "do shell script \\"rm \'${source}\'\\" with administrator privileges"`;
						await promisify(exec)(command);
					} catch (error) {
						throw new Error(`Unable to uninstall the shell command '${source}'.`);
					}
					break;
				}
				case 'ENOENT':
					break; // ignore file not found
				default:
					throw error;
			}
		}
	}

}
