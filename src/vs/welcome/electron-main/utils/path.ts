import * as path from 'path';
import { platform } from 'os';

const { join } = path;
const env = process.env;
const platformName = platform();

export const vscodeForkProductNameMap = {
	// nameShort
	'Code': {
		// darwinExecutable || win32RegValueName
		regName: 'VSCode',
		dataFolderName: '.vscode'
	},
	'Cursor': {
		regName: 'Cursor',
		dataFolderName: '.cursor'
	},
	'Trae': {
		regName: 'Trae',
		dataFolderName: '.trae'
	},
};

export type VSCodeIdeType = (keyof typeof vscodeForkProductNameMap) | string;

/**
 * Utility class for managing VSCode and its fork paths
 */
export class VSCodePathUtils {
	private dataFolderName: string;
	constructor(readonly nameShort: VSCodeIdeType, dataFolderName?: string) {
		// @ts-ignore
		this.dataFolderName = dataFolderName || vscodeForkProductNameMap[nameShort]?.dataFolderName || `.${nameShort.toLowerCase()}`; // .kwaipilot
	}

	/**
	 * Get user data directory path based on platform
	 */
	getUserDataPath(nameShort?: string): string {
		const name = nameShort ?? this.nameShort;
		switch (platformName) {
			case 'win32':
				return join(env['APPDATA']!, name);
			case 'darwin':
				return join(this.getHomeDir(), 'Library', 'Application Support', name);
			default: // Linux and others
				return join(this.getHomeDir(), '.config', name);
		}
	}

	/**
	 * Get extensions.json path (e.g. ~/.vscode/extensions/extensions.json)
	 */
	getExtensionsPath(): string {
		return join(this.getHomeDir(), `${this.dataFolderName}`, 'extensions', 'extensions.json');
	}
	/**
	 * Get keybindings.json path (e.g. ~/Library/Application Support/Code/User/keybindings.json)
	 */
	getKeybindingsPath(): string {
		return join(this.getUserDataPath(), 'User', 'keybindings.json');
	}

	/**
	 * Get user settings.json path
	 */
	getUserSettingsPath(): string {
		return join(this.getUserDataPath(), 'User', 'settings.json');
	}

	/**
	 * Get machine-specific settings.json path
	 */
	getMachineSettingsPath(): string {
		return join(this.getUserDataPath(), 'Machine', 'settings.json');
	}

	getMcpSettingsPath(): string {
		// /Users/<USER>/.cursor/mcp.json
		if (this.nameShort === 'Cursor') {
			return join(this.getHomeDir(), `${this.dataFolderName}`, 'mcp.json');
		}
		// ~/Library/Application Support/Trae/User/mcp.json
		if (this.nameShort === 'Trae') {
			return join(this.getUserDataPath(), `${this.dataFolderName}`, 'mcp.json');
		}
		// ~/Library/Application Support/Code/User/settings.json
		if (this.nameShort === 'Code') {
			return this.getUserSettingsPath();
		}
		return join(this.getHomeDir(), `${this.dataFolderName}`, 'mcp.json');
	}

	/**
	 * Get user home directory
	 * Alternative implementation for os.homedir()
	 */
	getHomeDir(): string {
		return env['HOME'] || env['USERPROFILE'] || '';
	}
}
