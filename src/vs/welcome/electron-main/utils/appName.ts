import * as path from 'path';
import * as fs from 'original-fs';
import { product } from '../../../../bootstrap-meta.js';

export function getAppName(isDev: boolean, dirname: string): string {
	if (isDev) {
		const packageJsonPath = path.join(dirname, '../package.json');
		const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf-8'));
		return packageJson.name;
	} else {
		const productJsonPath = path.join(dirname, '../product.json');
		const productJson = JSON.parse(fs.readFileSync(productJsonPath, 'utf-8'));
		return productJson.nameLong || productJson.applicationName;
	}
}

// product.nameShort ?? 'code-oss-dev'
export function getProductNameShort() {
	return product.nameShort ?? 'code-oss-dev';
}

export function getDataFolderName() {
	let dataFolderName = product.dataFolderName;
	if (process.env['VSCODE_DEV']) {
		dataFolderName = `${dataFolderName}-dev`;
	}
	return dataFolderName || `.${product.applicationName?.toLocaleLowerCase() || 'kwaipilot'}`;
}
