<!DOCTYPE html>
<html lang="en" data-theme="Dark-Modern">
	<head>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<title>Kwaipilot</title>
		<link rel="stylesheet" href="./styles/main.css" />
		<link rel="stylesheet" href="./styles/carousel.css" />
	</head>
	<body>
		<div class="container">
			<div class="carousel">
				<div class="carousel-inner">
					<!-- Page 1: Welcome -->
					<div class="carousel-slide welcome">
						<div class="content-frame">
							<div class="main-title">Kwaipilot</div>
							<div class="sub-title">Welcome to AI Code Editor</div>
							<div class="desc-title">Code Smarter, Build Faster</div>
						</div>
						<div class="control-frame">
							<div class="primary-button" onclick="func.nextSlide()">
								Get Started
							</div>
						</div>
					</div>
					<!-- Page 2: Import Settings -->
					<div class="carousel-slide import-settings">
						<div class="content-frame">
							<div class="sub-title">Import configuration</div>
							<div class="desc-title">
								Import your common settings from VS Code or Cursor, including
								extensions, settings, shortcut keys and MCP settings.
							</div>
						</div>
						<div class="control-frame">
							<div class="secondary-button" data-click-id="import-code" onclick="func.importFrom('Code')">
								<span class="loading-spinner"></span>
								<div class="icon icon-vscode"></div>
								<div class="import-option-title">Import from VS Code</div>
							</div>
							<div class="secondary-button" data-click-id="import-cursor" onclick="func.importFrom('Cursor')">
								<span class="loading-spinner"></span>
								<div class="icon icon-cursor"></div>
								<div class="import-option-title">Import from Cursor</div>
							</div>
							<div class="link-button" onclick="func.nextSlide()">
								Skip and Continue
							</div>
						</div>
					</div>

					<!-- Page 3: Theme Selection -->
					<div class="carousel-slide theme-selection">
						<div class="content-frame">
							<div class="sub-title">Choose your theme</div>
							<div class="desc-title">
								Change theme anytime via menu or settings
							</div>
							<div class="theme-cards">
								<div
									class="theme-wrapper active"
									onclick="func.selectTheme('Dark-Modern')"
								>
									<div class="theme-card">
										<img
											src="./assets/images/Dark-Modern.svg"
											alt="Dark Modern"
										/>
									</div>
									<div class="theme-name">Dark Modern</div>
								</div>
								<div
									class="theme-wrapper"
									onclick="func.selectTheme('Light-Modern')"
								>
									<div class="theme-card">
										<img
											src="./assets/images/Light-Modern.svg"
											alt="Light Modern"
										/>
									</div>
									<div class="theme-name">Light Modern</div>
								</div>
								<div
									class="theme-wrapper"
									onclick="func.selectTheme('Tomorrow-Night-Blue')"
								>
									<div class="theme-card">
										<img
											src="./assets/images/Tomorrow-Night-Blue.svg"
											alt="Tomorrow-Night-Blue"
										/>
									</div>
									<div class="theme-name">Tomorrow Night Blue</div>
								</div>
							</div>
						</div>
						<div class="control-frame">
							<div class="secondary-button" onclick="func.nextSlide()">Continue</div>
						</div>
					</div>

					<!-- Page 4: Command Line Tool -->
					<div class="carousel-slide cli-tool">
						<div class="content-frame">
							<div class="sub-title">Add the command</div>
							<div class="desc-title">
								Start using the command `kwaipilot` in Terminal
							</div>
						</div>
						<div class="control-frame">
							<div class="secondary-button" id="installButton" onclick="func.installCLI()">
								<span class="loading-spinner"></span>
								<span class="install-title">Install `kwaipilot` command</span>
							</div>
							<div class="link-button" onclick="func.nextSlide()">
								Skip and Continue
							</div>
						</div>
					</div>
					<!-- Page 5: login -->
					<div class="carousel-slide login-page">
						<div class="content-frame">
							<div class="sub-title">Collaboration with Kwaipilot</div>
							<div class="desc-title">
								In order to serve you better, we need you to log in to use AI features
							</div>
						</div>
						<div class="control-frame">
							<div class="primary-button" id="loginButton" onclick="func.login()">
								<span class="loading-spinner"></span>
								<span class="login-title">Log in</span>
							</div>
							<div class="link-button" onclick="func.completeWelcome()">
								Skip and Continue
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="carousel-dots"></div>
		</div>
		<script src="./js/welcome.js"></script>
	</body>
</html>
