/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import { createDecorator } from '../../../platform/instantiation/common/instantiation.js';
import { IExtensionManagementService, IExtensionGalleryService } from '../../../platform/extensionManagement/common/extensionManagement.js';
import { ILogService } from '../../../platform/log/common/log.js';
import { InstantiationType, registerSingleton } from '../../../platform/instantiation/common/extensions.js';
import { VSCodeIdeType, VSCodePathUtils } from '../../environment/common/environmentService.js';
import { IFileService } from '../../files/common/files.js';
import { URI } from '../../../base/common/uri.js';
import { IUserDataProfilesService } from '../../../platform/userDataProfile/common/userDataProfile.js';
import { dirname } from '../../../base/common/path.js';
import { VSBuffer } from '../../../base/common/buffer.js';
import { IProductService } from '../../product/common/productService.js';
import { isEngineValid } from '../../extensions/common/extensionValidator.js';
import { CancellationToken } from '../../../base/common/cancellation.js';


export interface IImportExtensionInfo {
	id: string;
	displayName: string;
	iconUrl: string;
	version: string;
	error: string;
}

// Import result interface
export interface IExtensionImportResult {
	success: boolean;
	failedExtensions: Array<IImportExtensionInfo>;
}

export interface ILocalExtensionExportInfo {
	identifier: {
		id: string;
		uuid?: string;
	};
	version: string;
	location: {
		$mid: number;
		path: string;
		scheme: string;
	};
	relativeLocation: string;
	metadata: {
		id?: string;
		publisherId?: string;
		publisherDisplayName?: string;
		targetPlatform?: string;
		updated?: boolean;
		isPreReleaseVersion?: boolean;
		installedTimestamp: number;
		preRelease?: boolean;
		source?: string;
		pinned?: boolean;
		isApplicationScoped?: boolean;
		isMachineScoped?: boolean;
		isBuiltin?: boolean;
		hasPreReleaseVersion?: boolean;
		private?: boolean;
	};
}

// Service interface
export const IExtensionsSyncService = createDecorator<IExtensionsSyncService>('IExtensionsSyncService');

export interface IExtensionsSyncService {
	readonly _serviceBrand: undefined;

	// Import extensions from a product, returns import result
	importExtensions(productId: VSCodeIdeType, onProgress?: (message: string) => void): Promise<IExtensionImportResult>;
}

// Service implementation
export class ExtensionsSyncService implements IExtensionsSyncService {
	readonly _serviceBrand: undefined;

	constructor(
		@IExtensionManagementService private readonly extensionManagementService: IExtensionManagementService,
		@ILogService private readonly logService: ILogService,
		@IFileService private readonly fileService: IFileService,
		@IUserDataProfilesService private readonly userDataProfilesService: IUserDataProfilesService,
		@IProductService private readonly productService: IProductService,
		@IExtensionGalleryService private readonly extensionGalleryService: IExtensionGalleryService,
	) { }

	async importExtensions(productId: VSCodeIdeType, onProgress?: (message: string) => void): Promise<IExtensionImportResult> {
		const failedExtensions: Array<IImportExtensionInfo> = [];

		try {
			// 1. Get target product's extension list
			const extensions = await this.getTargetExtensions(productId);
			if (!extensions || extensions.length === 0) {
				this.logService.info(`No extensions found for ${productId}`);
				return { success: true, failedExtensions };
			}
			// 2. Get currently installed extensions
			const installedExtensions = await this.extensionManagementService.getInstalled();
			// 3. Filter extensions that need to be installed
			const extensionsToInstall = extensions.filter(ext => {
				const installed = installedExtensions.find(installed =>
					installed.identifier.id.toLowerCase() === ext.identifier.id.toLowerCase()
				);

				if (!installed) {
					return true;
				}

				if (installed.manifest.version === ext.version) {
					return false;
				}

				const installedTime = installed?.installedTimestamp || 0;
				const targetTime = ext.metadata?.installedTimestamp || 0;

				return targetTime > installedTime;
			});

			if (extensionsToInstall.length === 0) {
				this.logService.info(`All extensions from ${productId} are already installed`);
				return { success: true, failedExtensions };
			}

			onProgress?.(`Validating ${extensionsToInstall.length} extensions...`);

			// Check each plugin's availability in the marketplace
			const marketplaceVerificationResults = await Promise.all(
				extensionsToInstall.map(async extension => {
					try {
						const gallery = await this.extensionManagementService.getExtensionsControlManifest();
						const extensionId = extension.identifier.id;

						const isAvailable = gallery?.malicious.indexOf(extensionId) === -1;

						if (!isAvailable) {
							const extInfo = await this.getExtensionInfo(extension);
							failedExtensions.push({
								id: extensionId,
								displayName: extInfo.displayName,
								iconUrl: extInfo.iconUrl,
								version: extension.version,
								error: 'Extension is not available in the current marketplace'
							});
							return false;
						}
						return true;
					} catch (error) {
						const extInfo = await this.getExtensionInfo(extension);
						failedExtensions.push({
							id: extension.identifier.id,
							displayName: extInfo.displayName,
							iconUrl: extInfo.iconUrl,
							version: extension.version,
							error: `Failed to verify marketplace availability: ${error.message}`
						});
						return false;
					}
				})
			);

			const availableExtensions = extensionsToInstall.filter((_, index) => marketplaceVerificationResults[index]);

			if (availableExtensions.length === 0) {
				this.logService.info('No available extensions to import');
				return {
					success: false,
					failedExtensions
				};
			}

			onProgress?.(`Checking VSCode version compatibility for ${availableExtensions.length} extensions...`);

			// Check VSCode version compatibility for each extension
			const versionCompatibilityResults = await Promise.all(
				availableExtensions.map(async extension => {
					try {
						const extInfo = await this.getExtensionInfo(extension);
						const manifest = await this.getExtensionManifest(extension);

						if (!manifest || !manifest.engines || !manifest.engines.vscode) {
							// If no engine requirement is specified, assume it's compatible
							return true;
						}

						const isCompatible = isEngineValid(
							manifest.engines.vscode,
							this.productService.version,
							this.productService.date
						);

						if (!isCompatible) {
							failedExtensions.push({
								id: extension.identifier.id,
								displayName: extInfo.displayName,
								iconUrl: extInfo.iconUrl,
								version: extension.version,
								error: `Extension requires VSCode ${manifest.engines.vscode}, but current version is ${this.productService.version}`
							});
							return false;
						}
						return true;
					} catch (error) {
						const extInfo = await this.getExtensionInfo(extension);
						failedExtensions.push({
							id: extension.identifier.id,
							displayName: extInfo.displayName,
							iconUrl: extInfo.iconUrl,
							version: extension.version,
							error: `Failed to check version compatibility: ${error.message}`
						});
						return false;
					}
				})
			);

			const compatibleExtensions = availableExtensions.filter((_, index) => versionCompatibilityResults[index]);

			if (compatibleExtensions.length === 0) {
				this.logService.info('No compatible extensions to import');
				return {
					success: false,
					failedExtensions
				};
			}

			// Get the URI of the current profile's extensions resource
			const extensionsJsonPath = this.userDataProfilesService.defaultProfile.extensionsResource;
			const curExtensionsJson = await this.getCurrentExtensions();

			onProgress?.(`Importing ${compatibleExtensions.length} compatible extensions...`);

			// 4. Install extensions
			for (const extension of compatibleExtensions) {
				try {
					const sourcePath = URI.file(extension.location.path);

					const targetPath = URI.file(dirname(extensionsJsonPath.fsPath));

					// Copy extension files
					await this.copyFolder(sourcePath, targetPath);

					// Add extension to extensionsJsonPath file
					curExtensionsJson.push(extension);

					onProgress?.(`Importing extension ${extension.identifier.id} ${extension.version}...`);

					this.logService.info(`Successfully imported extension: ${extension.identifier.id}`);
				} catch (error) {
					this.logService.error(`Failed to import extension ${extension.identifier.id}:`, error);
					const extInfo = await this.getExtensionInfo(extension);
					failedExtensions.push({
						id: extension.identifier.id,
						displayName: extInfo.displayName,
						iconUrl: extInfo.iconUrl,
						version: extension.version,
						error: `Failed to import extension: ${error.message}`
					});
				}
			}
			try {
				await this.fileService.writeFile(extensionsJsonPath, VSBuffer.fromString(JSON.stringify(curExtensionsJson)));

			} catch (err) {
				this.logService.error(`Failed to write to ${extensionsJsonPath.fsPath}:`, err);
			}
			return {
				success: failedExtensions.length < compatibleExtensions.length,
				failedExtensions
			};

		} catch (error) {
			this.logService.error('Failed to import extensions:', error);
			throw error;
		}
	}

	private async getExtensionInfo(extension: ILocalExtensionExportInfo): Promise<{ displayName: string; iconUrl: string }> {
		try {
			// Try to read the manifest from the extension location
			const manifestPath = URI.file(`${extension.location.path}/package.json`);
			if (await this.fileService.exists(manifestPath)) {
				const manifestContent = await this.fileService.readFile(manifestPath);
				const manifest = JSON.parse(manifestContent.value.toString());

				const displayName = manifest.displayName || manifest.name || extension.identifier.id;
				let iconUrl = '';

				// Try to get remote icon URL from extension gallery
				try {
					if (this.extensionGalleryService.isEnabled()) {
						const galleryExtensions = await this.extensionGalleryService.getExtensions([{
							id: extension.identifier.id,
							uuid: extension.identifier.uuid
						}], CancellationToken.None);

						if (galleryExtensions.length > 0 && galleryExtensions[0].assets.icon) {
							iconUrl = galleryExtensions[0].assets.icon.uri;
						}
					}
				} catch (galleryError) {
					this.logService.warn(`Failed to get remote icon for extension ${extension.identifier.id}:`, galleryError);
					// Fallback to empty string if remote icon fetch fails
					iconUrl = '';
				}

				return { displayName, iconUrl };
			}
		} catch (error) {
			this.logService.warn(`Failed to read manifest for extension ${extension.identifier.id}:`, error);
		}

		// Fallback to basic information
		return {
			displayName: extension.identifier.id,
			iconUrl: ''
		};
	}

	private async getExtensionManifest(extension: ILocalExtensionExportInfo): Promise<any> {
		try {
			const manifestPath = URI.file(`${extension.location.path}/package.json`);
			if (await this.fileService.exists(manifestPath)) {
				const manifestContent = await this.fileService.readFile(manifestPath);
				return JSON.parse(manifestContent.value.toString());
			}
		} catch (error) {
			this.logService.warn(`Failed to read manifest for extension ${extension.identifier.id}:`, error);
		}
		return null;
	}

	private async getTargetExtensions(productId: VSCodeIdeType): Promise<Array<ILocalExtensionExportInfo>> {
		try {
			this.logService.info(`Getting installed extensions for ${productId}`);

			const extensionsFilePath = new VSCodePathUtils(productId).getExtensionsPath();
			try {
				const fileContent = await this.fileService.readFile(URI.file(extensionsFilePath));
				const list = JSON.parse(fileContent.value.toString()) as ILocalExtensionExportInfo[];
				const extMap = list.reduce<Record<string, ILocalExtensionExportInfo>>((acc, current) => {
					const { id } = current.identifier;
					if (!acc[id] || current.metadata.installedTimestamp > acc[id].metadata.installedTimestamp) {
						acc[id] = current;
					}
					return acc;
				}, Object.create(null));
				return Object.values(extMap);
			} catch (error) {
				this.logService.info(`Extensions file not found or failed to read for ${productId} at ${extensionsFilePath}`);
				return [];
			}

		} catch (error) {
			this.logService.error('Failed to get target extensions:', error);
			return [];
		}
	}

	private async getCurrentExtensions(): Promise<ILocalExtensionExportInfo[]> {
		try {
			const extensionsJsonPath = this.userDataProfilesService.defaultProfile.extensionsResource;
			const curExtensionJson = JSON.parse((await this.fileService.readFile(extensionsJsonPath)).value.toString());
			return curExtensionJson;
		} catch (error) {
			this.logService.error('Failed to get current extensions:', error);
			return [];
		}
	}

	private async copyFolder(sourcePath: URI, targetPath: URI): Promise<void> {
		try {
			const sourceFolderName = sourcePath.fsPath.split('/').pop();
			const targetParentFolder = targetPath.fsPath;

			const targetAllPath = URI.file(`${targetParentFolder}/${sourceFolderName}`);

			if (await this.fileService.exists(sourcePath)) {
				await this.fileService.copy(sourcePath, targetAllPath, true);
				console.log(`Successfully copied ${sourceFolderName} to ${targetAllPath.fsPath}`);
			} else {
				console.error(`Source folder ${sourceFolderName} does not exist`);
			}
		} catch (error) {
			console.error(`Error copying folder: ${error}`);
			throw error;
		}
	}
}

// private async installExtension(extensionId: string): Promise<void> {
// 	try {
//
// 		const galleryExtension = await this.extensionManagementService.getExtensionsControlManifest();
// 		if (!galleryExtension) {
// 			throw new Error(`Extension ${extensionId} not found in gallery`);
// 		}

//
// 		await this.extensionManagementService.installFromGallery(
// 			{
// 				identifier: { id: extensionId },
// 				type: 'gallery'
// 			} as any,
// 			{
// 				isMachineScoped: false,
// 				installGivenVersion: true,
// 			}
// 		);

// 		this.logService.info(`Successfully installed extension: ${extensionId}`);
// 	} catch (error) {
// 		this.logService.error(`Failed to install extension ${extensionId}:`, error);
// 		throw error;
// 	}
// }
registerSingleton(IExtensionsSyncService, ExtensionsSyncService, InstantiationType.Delayed);
