/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import { localize } from '../../../../nls.js';
import { $, append, getActiveWindow } from '../../../../base/browser/dom.js';
import { IDialogService } from '../../../../platform/dialogs/common/dialogs.js';
import './extensionFailureDialog.css';
import { IImportExtensionInfo } from '../../../../platform/extensionsSync/browser/extensionsSyncService.js';


// 方法1：使用自定义Modal对话框 - 完全自由的HTML控制
export function showExtensionFailureDialog(_dialogService: IDialogService, failedExtensions: IImportExtensionInfo[]): void {
	showCustomModalDialog(failedExtensions);
}

// 实现方法1：自定义Modal对话框
function showCustomModalDialog(failedExtensions: IImportExtensionInfo[]): void {
	const activeWindow = getActiveWindow();
	// 找到monaco-workbench容器，确保VSCode主题token生效
	const workbenchContainer = activeWindow.document.querySelector('.monaco-workbench') as HTMLElement;
	if (!workbenchContainer) {
		console.error('Could not find .monaco-workbench container');
		return;
	}

	// 创建遮罩层
	const overlay = append(workbenchContainer, $('.extension-failure-overlay'));

	// 创建对话框容器
	const dialog = append(overlay, $('.extension-failure-dialog'));

	// 创建标题栏
	const header = append(dialog, $('.dialog-header'));

	const titleContainer = append(header, $('.title-container'));

	const title = append(titleContainer, $('.dialog-title'));
	title.textContent = localize('importFailureTitle', "Import Failed");

	const closeButton = append(header, $('.close-button'));
	closeButton.classList.add('codicon', 'codicon-close');

	// 创建内容区域
	const content = append(dialog, $('.dialog-content'));

	const description = append(content, $('.description'));

	// Create the first part of the text
	const textPart = document.createTextNode(localize('extensionImportFailureText', "Failed to import {0} extensions. ", failedExtensions.length));
	description.appendChild(textPart);

	// Create the clickable link
	const link = append(description, $('a.solution-link')) as HTMLAnchorElement;
	link.textContent = localize('extensionImportFailureLinkText', "Click to view solutions");
	link.href = 'https://docs.corp.kuaishou.com/d/home/<USER>';
	link.target = '_blank';
	link.rel = 'noopener noreferrer';

	// Add period after the link
	const periodPart = document.createTextNode('.');
	description.appendChild(periodPart);

	// 创建扩展列表
	const extensionList = append(content, $('.extension-list'));

	failedExtensions.forEach((ext) => {
		const item = append(extensionList, $('.extension-item'));

		// 扩展图标
		const icon = append(item, $('img.extension-icon')) as HTMLImageElement;
		const defaultIcon = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAHgAAAB4CAMAAAAOusbgAAAABGdBTUEAALGPC/xhBQAAACBjSFJNAAB6JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAAATlBMVEUAAADx8vLx8vLx8vLx8vLx8vLx8vLx8vLx8vLx8vLx8vLx8vLx8vLx8vLx8vLx8vLx8vLe39/o6enj5OTY2dnl5uba29vr7Ozv8PD///+VH9SVAAAAEHRSTlMAQHCAv+8gYJ/fz1CPMBCv0SKY+gAAAAFiS0dEGexutYgAAAAHdElNRQfiCwYULSYs66paAAACjElEQVRo3u2b247iMAyGU1L3EEJxmWFm3/9J92IX1KKmcWo7Hmn4LxHSp1SxHZ+cK1Vz8m0L+BS0rT81TlVdP4yYUBj6Tod6jknoQ2M8S1MvEZAkiBc56tQHLFDoJxmsBywU+MkCK4I+hv2HZmCbERkajxr3dEWmroe+9xmQLSi36ymiiGLhobuAQgpFjrQBFBMU3LEeRdWTjReFRTTpAcU1GHFJZBUugexRSb7qfSbf7QYVtWPPHWiCIenDpoCqCim/HVFZMREHUV2bUXICfTBsfewrVtC1siXt2VTqXTendEPEj3lHH5svQLKr/Ezpjojz545miutM3yxp8Mv9SscGafD6yDumJA5eHXknGIqDl0fe8x3y4MWR96KwPHgRmQMbvLTfrxw4POsMyAavXE4OjBdKONQAP8Ij1AYDJQ5rgP/H5UgGz6QARADH3bikCB6dc67D+mDs8m94HXCfT5Zewd/J21YCHjJuSw0cnHNoAcb8I08J3LiTKJh8FU/ZhFgJ7F1rA27twGADhpw1aYHxDWaCs0mbFpiuN5gJvi90e5vTGmzmMn9fdDJ7CJg9fRobcFP8vP3znVIROJuzFaYw1H+H8hRG5t+DdNJG/XdflqZ+bVVwb8sfqOCuLDHfLEfci+tcj+JxrA+OnOILB3zmlJsYYGAV2BjgyCopMsAXVhH1ODjwysbHwT2vUH4YvOwN+Jpgz2yGHAWv+z++HthXb6VuN1R9LbCnNjWFNf6cNq5Z49quVW82nGA3jmE3gGI2cmM3ZGQ3VmU3SGY3Omc3LGg3Hmk3EGo3Ams39Gs35mw32G03ym44vG+3rmC4oGG4kmK3hGO4dmS4aGW5WvZcpkt+dL1lOuH1wb8Dn/C3GCuwnwAAACV0RVh0ZGF0ZTpjcmVhdGUAMjAxOC0xMS0wNlQyMDo0NTozOCswMDowMCjQntoAAAAldEVYdGRhdGU6bW9kaWZ5ADIwMTgtMTEtMDZUMjA6NDU6MzgrMDA6MDBZjSZmAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAABJRU5ErkJggg==';
		icon.src = ext.iconUrl || defaultIcon;
		icon.alt = ext.displayName;
		icon.title = ext.displayName;
		// Fallback to default icon if the extension icon fails to load
		icon.onerror = () => {
			icon.src = defaultIcon;
		};

		// 内容区域
		const itemContent = append(item, $('.item-content'));

		// 扩展名称和版本号容器
		const nameContainer = append(itemContent, $('.extension-name-container'));

		// 扩展名称
		const name = append(nameContainer, $('.extension-name'));
		name.textContent = ext.displayName;

		// 版本号
		if (ext.version) {
			const version = append(nameContainer, $('.extension-version'));
			version.textContent = ext.version;
		}

		// 错误信息
		const error = append(itemContent, $('.extension-tip'));
		error.textContent = ext.error;

		// 错误图标 - 红色的X
		const errorIcon = append(item, $('.item-error-icon'));
		errorIcon.classList.add('codicon', 'codicon-close');
	});

	// 关闭对话框的函数
	const closeDialog = () => {
		overlay.remove();
		activeWindow.document.removeEventListener('keydown', handleKeyDown);
	};

	// 绑定事件 - 只有关闭按钮可以关闭，不允许点击overlay关闭
	closeButton.addEventListener('click', closeDialog);

	// ESC键关闭
	const handleKeyDown = (e: KeyboardEvent) => {
		if (e.key === 'Escape') {
			closeDialog();
		}
	};
	activeWindow.document.addEventListener('keydown', handleKeyDown);
}
