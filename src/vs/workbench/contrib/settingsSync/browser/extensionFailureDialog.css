/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

/* Extension Failure Dialog Styles */
.extension-failure-overlay {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background: rgba(0, 0, 0, 0.5);
	z-index: 10000;
	display: flex;
	align-items: center;
	justify-content: center;
}

.extension-failure-dialog {
	background: var(--vscode-editor-background);
	border: 1px solid var(--vscode-widget-border);
	border-radius: 6px;
	box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
	width: 650px;
	max-width: 90vw;
	max-height: 80vh;
	display: flex;
	flex-direction: column;
	overflow: hidden;
}

.dialog-header {
	padding: 20px 24px 16px 24px;
	border-bottom: 1px solid var(--vscode-widget-border);
	display: flex;
	align-items: center;
	justify-content: space-between;
	background: var(--vscode-editor-background);
}

.title-container {
	display: flex;
	align-items: center;
	gap: 10px;
}

.error-icon {
	color: var(--vscode-errorForeground);
	font-size: 18px;
}

.dialog-title {
	font-size: 18px;
	font-weight: 500;
	font-family: PingFang SC;
	letter-spacing: -0.36px;
	line-height: 25.2px;
	text-align: left;
	vertical-align: top;
	color: var(--vscode-foreground);
}

.close-button {
	color: var(--vscode-foreground);
	font-size: 16px;
	cursor: pointer;
	padding: 4px;
	border-radius: 3px;
	background: transparent;
	border: none;
}

.close-button:hover {
	background: var(--vscode-toolbar-hoverBackground);
}

.dialog-content {
	padding: 0 24px 24px 24px;
	flex: 1;
	overflow-y: auto;
}

.description {
	color: var(--vscode-descriptionForeground);
	font-size: 14px;
	line-height: 1.4;
	margin-bottom: 20px;
}

.extension-list {
	border: 1px solid var(--vscode-input-border);
	border-radius: 6px;
	background: var(--vscode-editor-background);
	max-height: 400px;
	overflow-y: auto;
}

.extension-item {
	display: flex;
	align-items: flex-start;
	padding: 16px 20px;
	gap: 12px;
	transition: background-color 0.1s ease;
}

.extension-item:not(:last-child) {
	border-bottom: 1px solid var(--vscode-input-border);
}

.extension-item:hover {
	background: var(--vscode-list-hoverBackground);
}

/* 扩展图标容器 */
.extension-icon-container {
	width: 24px;
	height: 24px;
	flex-shrink: 0;
	margin-top: 1px;
	display: flex;
	align-items: center;
	justify-content: center;
}

/* 扩展图标 - 圆形24x24 */
.extension-icon {
	width: 24px;
	height: 24px;
	border-radius: 50%;
	object-fit: cover;
}

/* 默认扩展图标 */
.extension-icon-default.codicon.codicon-extensions {
	display: flex;
	align-items: center;
	justify-content: center;
	color: var(--vscode-foreground);
	font-size: 16px;
	width: 24px;
	height: 24px;
	border-radius: 50%;
	background: var(--vscode-button-secondaryBackground);
}

.item-content {
	flex: 1;
	min-width: 0;
}

/* 扩展名称和版本号容器 */
.extension-name-container {
	display: flex;
	align-items: center;
	gap: 8px;
	margin-bottom: 6px;
}

.extension-name {
	color: var(--vscode-foreground);
	font-size: 13px;
	font-weight: 500;
	font-family: 'PingFang SC';
	letter-spacing: 0px;
	line-height: 18.2px;
	text-align: left;
	vertical-align: top;
}

/* 版本号样式 */
.extension-version {
	display: flex;
	flex-direction: row;
	align-items: center;
	padding: 0 4px;
	border-style: solid;
	border-width: 0.6px;
	border-color: var(--vscode--checkbox-border);
	border-radius: 4px;
	color: var(--vscode-descriptionForeground);
	font-size: 11px;
	font-weight: 400;
	font-family: 'PingFang SC';
	letter-spacing: 0px;
	line-height: 15.4px;
	text-align: left;
	vertical-align: middle;
}

.extension-tip {
	color: var(--vscode-descriptionForeground);
	font-size: 13px;
	line-height: 1.4;
	word-break: break-word;
	opacity: 0.9;
}

/* 右侧红色X图标 */
.item-error-icon.codicon.codicon-close {
	color: var(--vscode-errorForeground);
	font-size: 16px;
	margin-top: 1px;
	flex-shrink: 0;
	opacity: 0.8;
	cursor: pointer;
}

.item-error-icon.codicon.codicon-close:hover {
	opacity: 1;
}

/* 解决方案链接样式 */
.solution-link {
	color: var(--vscode-textLink-foreground);
	text-decoration: none;
}

.solution-link:hover {
	color: var(--vscode-textLink-activeForeground);
	text-decoration: underline;
}
