/** 这里需要还原 插件在 webview 环境下缺失的 css  _defaultStyles 在 pre/index.html */
[data-element-type="html"] {
	scrollbar-color: var(--vscode-scrollbarSlider-background)
		var(--vscode-editor-background);
}
[data-element-type="body"] {
	overscroll-behavior-x: none;
	background-color: transparent;
	color: var(--vscode-editor-foreground);
	font-family: var(--vscode-font-family);
	font-weight: var(--vscode-font-weight);
	font-size: var(--vscode-font-size);
	margin: 0;
	padding: 0 20px;
}

.ai-assistant-container img,
.ai-assistant-container video {
	max-width: 100%;
	max-height: 100%;
}

.ai-assistant-container p > a {
	text-decoration: var(--text-link-decoration);
}

.ai-assistant-container a:hover {
	color: var(--vscode-textLink-activeForeground);
}

.ai-assistant-container a:focus,
.ai-assistant-container input:focus,
.ai-assistant-container select:focus,
.ai-assistant-container textarea:focus {
	outline: 1px solid -webkit-focus-ring-color;
	outline-offset: -1px;
}

.ai-assistant-container code {
	font-family: var(--monaco-monospace-font);
	color: var(--vscode-textPreformat-foreground);
	background-color: var(--vscode-textPreformat-background);
	padding: 1px 3px;
	border-radius: 4px;
}

.ai-assistant-container pre code {
	padding: 0;
}

.ai-assistant-container blockquote {
	background: var(--vscode-textBlockQuote-background);
	border-color: var(--vscode-textBlockQuote-border);
}

.ai-assistant-container kbd {
	background-color: var(--vscode-keybindingLabel-background);
	color: var(--vscode-keybindingLabel-foreground);
	border-style: solid;
	border-width: 1px;
	border-radius: 3px;
	border-color: var(--vscode-keybindingLabel-border);
	border-bottom-color: var(--vscode-keybindingLabel-bottomBorder);
	box-shadow: inset 0 -1px 0 var(--vscode-widget-shadow);
	vertical-align: middle;
	padding: 1px 3px;
}

.ai-assistant-container ::-webkit-scrollbar {
	width: 10px;
	height: 10px;
}

.ai-assistant-container ::-webkit-scrollbar-corner {
	background-color: var(--vscode-editor-background);
}

.ai-assistant-container ::-webkit-scrollbar-thumb {
	background-color: var(--vscode-scrollbarSlider-background);
}
.ai-assistant-container ::-webkit-scrollbar-thumb:hover {
	background-color: var(--vscode-scrollbarSlider-hoverBackground);
}
.ai-assistant-container ::-webkit-scrollbar-thumb:active {
	background-color: var(--vscode-scrollbarSlider-activeBackground);
}
.ai-assistant-container ::highlight(find-highlight) {
	background-color: var(--vscode-editor-findMatchHighlightBackground);
}
.ai-assistant-container ::highlight(current-find-highlight) {
	background-color: var(--vscode-editor-findMatchBackground);
}

.pane-body .ai-assistant-container *,
.pane-body .ai-assistant-container :before,
.pane-body .ai-assistant-container :after {
	border-color: var(--chakra-colors-chakra-border-color);
}
