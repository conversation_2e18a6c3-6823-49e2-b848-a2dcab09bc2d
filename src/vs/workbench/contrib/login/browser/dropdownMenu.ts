import { addDisposableListener, EventType, getActiveWindow, clearNode } from '../../../../base/browser/dom.js';
import { Disposable, IDisposable } from '../../../../base/common/lifecycle.js';
import './media/dropdownMenu.css';

export interface IDropdownMenuOptions {
	triggerElement: HTMLElement;
	container: HTMLElement;
	menuItems: IDropdownMenuItem[];
	onSelect?: (item: IDropdownMenuItem) => void;
}

export type IDropdownMenuItem = {
	id: string;
	label: string | HTMLElement;
	icon?: string;
	disabled?: boolean;
} | {
	separator: boolean;
};

export class DropdownMenu extends Disposable {
	private menuElement!: HTMLElement;
	private visible: boolean = false;
	private disposables: IDisposable[] = [];
	// 存储菜单项ID与DOM元素的映射关系
	private menuItemMap: Map<string, HTMLElement> = new Map();

	constructor(private options: IDropdownMenuOptions) {
		super();

		this.create();
		this.registerListeners();
	}

	private create(): void {
		// Create menu container
		this.menuElement = document.createElement('div');
		this.menuElement.className = 'dropdown-menu';
		this.options.container.appendChild(this.menuElement);

		// Create menu items
		this.options.menuItems.forEach(item => {
			if ('separator' in item) {
				const divider = document.createElement('div');
				divider.className = 'dropdown-menu-divider';
				this.menuElement.appendChild(divider);
			} else {
				const menuItem = document.createElement('div');
				menuItem.className = 'dropdown-menu-item';
				if (item.disabled) {
					menuItem.classList.add('disabled');
				}

				if (item.icon) {
					const icon = document.createElement('span');
					icon.className = `icon ${item.icon}`;
					menuItem.appendChild(icon);
				}

				const text = document.createElement('span');
				text.className = 'text';
				if (typeof item.label === 'string') {
					text.textContent = item.label;
				} else {
					text.appendChild(item.label);
				}
				menuItem.appendChild(text);

				if (!item.disabled) {
					menuItem.addEventListener('click', () => {
						this.options.onSelect?.(item);
						this.hide();
					});
				}

				this.menuElement.appendChild(menuItem);

				// 存储菜单项ID与DOM元素的映射
				this.menuItemMap.set(item.id, menuItem);
			}
		});
	}

	/**
	 * 根据ID查找菜单项DOM元素
	 */
	private findMenuItemById(id: string): HTMLElement | undefined {
		return this.menuItemMap.get(id);
	}

	/**
	 * 动态更新指定菜单项的内容
	 */
	public updateMenuItem(id: string, newLabel: string | HTMLElement): void {
		const menuItem = this.findMenuItemById(id);
		if (!menuItem) {
			return;
		}

		const textElement = menuItem.querySelector('.text');
		if (textElement) {
			// 使用安全的方式清空现有内容，避免TrustedHTML错误
			clearNode(textElement as HTMLElement);

			if (typeof newLabel === 'string') {
				textElement.textContent = newLabel;
			} else {
				textElement.appendChild(newLabel);
			}
		}
	}

	/**
	 * 动态设置指定菜单项的禁用状态
	 */
	public setMenuItemDisabled(id: string, disabled: boolean): void {
		const menuItem = this.findMenuItemById(id);
		if (!menuItem) {
			return;
		}

		if (disabled) {
			menuItem.classList.add('disabled');
			// 移除点击事件监听器
			const newMenuItem = menuItem.cloneNode(true) as HTMLElement;
			menuItem.parentNode?.replaceChild(newMenuItem, menuItem);
			// 更新映射
			this.menuItemMap.set(id, newMenuItem);
		} else {
			menuItem.classList.remove('disabled');
			// 重新添加点击事件监听器
			const originalItem = this.options.menuItems.find(item => 'id' in item && item.id === id);
			if (originalItem && 'id' in originalItem) {
				menuItem.addEventListener('click', () => {
					this.options.onSelect?.(originalItem);
					this.hide();
				});
			}
		}
	}

	private registerListeners(): void {
		const targetWindow = getActiveWindow();
		// Toggle menu on trigger click
		this._register(addDisposableListener(this.options.triggerElement, EventType.CLICK, (e) => {
			e.preventDefault();
			e.stopPropagation();
			this.toggle();
		}));

		// Hide menu when clicking outside
		this._register(addDisposableListener(targetWindow.document, EventType.CLICK, (e) => {
			if (!this.menuElement.contains(e.target as Node) &&
				!this.options.triggerElement.contains(e.target as Node)) {
				this.hide();
			}
		}));

		// Hide menu when pressing escape
		this._register(addDisposableListener(targetWindow.document, EventType.KEY_DOWN, (e) => {
			if (e.key === 'Escape') {
				this.hide();
			}
		}));
	}

	// private updatePosition(): void {
	// 	const targetWindow = getActiveWindow();
	// 	const triggerRect = this.options.triggerElement.getBoundingClientRect();
	// 	const menuRect = this.menuElement.getBoundingClientRect();
	// 	const viewportHeight = targetWindow.innerHeight;

	// 	// Position menu below trigger by default
	// 	let top = triggerRect.bottom;
	// 	let left = triggerRect.left;

	// 	// If menu would go below viewport, position it above trigger instead
	// 	if (top + menuRect.height > viewportHeight) {
	// 		top = triggerRect.top - menuRect.height;
	// 	}

	// 	// If menu would go off the right edge of the viewport, adjust its left position
	// 	if (left + menuRect.width > targetWindow.innerWidth) {
	// 		left = targetWindow.innerWidth - menuRect.width;
	// 	}

	// 	this.menuElement.style.top = `${top}px`;
	// 	this.menuElement.style.left = `${left}px`;
	// }

	public show(): void {
		if (!this.visible) {
			this.menuElement.classList.add('show');
			// this.updatePosition();
			this.visible = true;
		}
	}

	public hide(): void {
		if (this.visible) {
			this.menuElement.classList.remove('show');
			this.visible = false;
		}
	}

	public toggle(): void {
		this.visible ? this.hide() : this.show();
	}

	override dispose(): void {
		super.dispose();
		this.menuElement.remove();
		this.disposables.forEach(d => d.dispose());
		this.disposables = [];
	}
}
