.dropdown {
	position: relative;
	display: inline-block;
}

.dropdown-menu {
	position: fixed;
	top: 44px;
	right: 10px;
	min-width: 200px;
	background-color: var(--vscode-menu-background);
	border: 1px solid var(--vscode-menu-border);
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
	border-radius: 4px;
	padding: 8px 12px;
	z-index: 1000;
	display: none;
	border-radius: 6px;
}

.dropdown-menu.show {
	display: block;
}

.dropdown-menu-item {
	display: flex;
	align-items: center;
	padding: 6px 12px;
	color: var(--vscode-menu-foreground);
	cursor: pointer;
	user-select: none;
	white-space: nowrap;
	font-family: "SF Pro Text";
	font-weight: 500;
	font-size: 13px;
}

.dropdown-menu-item:hover {
	color: var(--vscode-menu-selectionForeground);
	background-color: var(--vscode-menu-selectionBackground);
}

.dropdown-menu-item .icon {
	margin-right: 8px;
	font-size: 14px;
}

.dropdown-menu-item .text {
	flex: 1;
}

.dropdown-menu-divider {
	height: 1px;
	margin: 4px 0;
	background-color: var(--vscode-menu-separatorBackground);
}

.dropdown-trigger {
	display: flex;
	align-items: center;
	padding: 4px 8px;
	background-color: var(--vscode-button-background);
	color: var(--vscode-button-foreground);
	border: none;
	border-radius: 2px;
	cursor: pointer;
}

.dropdown-trigger:hover {
	background-color: var(--vscode-button-hoverBackground);
}

.dropdown-trigger .icon {
	margin-right: 4px;
}

.dropdown-menu-item .layout-frame {
	display: flex;
	align-items: center;
	gap: 10px;
}

.dropdown-menu-item .layout-frame .avatar {
	width: 24px;
	height: 24px;
	border-radius: 50%;
	border:1px solid var(--vscode-foreground);
}

.dropdown-menu-item .layout-frame .user-name {
	box-sizing: border-box;
	display: flex;
	align-items: center;
	color: var(--vscode-foreground);
	font-size: 13px;
	vertical-align: middle;
	line-height: 15.51px;
	font-family: "SF Pro Text";
	font-weight: 500;
}

.dropdown-menu-item .btn {
	display: flex;
	align-items: center;
	justify-content: center;
	height: 32px;
	background-color: var(--vscode-button-background);
	color: var(--vscode-foreground);
	border-radius: 4px;
	font-size: 13px;
	vertical-align: middle;
	line-height: 15.51px;
	font-family: "SF Pro Text";
	font-weight: 500;
}

.dropdown-menu-item .btn:hover {
	background-color: var(--vscode-button-hoverBackground);
}

/* 禁用状态样式 */
.dropdown-menu-item.disabled {
	cursor: default;
	color: var(--vscode-disabledForeground);
	pointer-events: none;
}

.dropdown-menu-item.disabled:hover {
	color: var(--vscode-disabledForeground);
	background-color: transparent;
}

.dropdown-menu-item.disabled .icon {
	opacity: 0.6;
}

.dropdown-menu-item.disabled .text {
	opacity: 0.6;
}

.dropdown-menu-item.disabled .btn {
	opacity: 0.6;
	cursor: default;
	pointer-events: none;
}

.dropdown-menu-item.disabled .btn:hover {
	background-color: var(--vscode-button-background);
}
