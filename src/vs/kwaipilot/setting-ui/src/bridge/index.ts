import {
  ArtifactPreviewData,
  NATIVE_BRIDGE_EVENT_NAME,
  WEBVIEW_BRIDGE_EVENT_NAME,
  WebviewBridgeParams,
} from "@shared/types/bridge";
import { KwaiPilotBridge } from "./kwaipilotBridge";
import { LoggerSupplementaryField, ReportKeys, ReportOpt } from "shared/lib/misc/logger";
import { ObservableAPI, ResponseMessage } from "shared";
import { Observable } from "rxjs";
import { v4 as uuidv4 } from "uuid";
import { IRPCProtocol } from "shared/lib/bridge/proxyIdentifier";
import { createWebviewRpcContext } from "./WebviewRpcContext";
import { logger } from "@/utils/logger";
import { ExtensionContext } from "shared/lib/bridge/protocol";

export class KwaiPilotBridgeAPI {
  private bridge: KwaiPilotBridge;
  private loggerScope = "KwaiPilotBridgeAPI";

  constructor() {
    this.bridge = new KwaiPilotBridge();
    this.observableAPI = createObservableAPI(this.bridge);
    this.rpcContext = createWebviewRpcContext({
      logger: () => logger,
      protocol: {
        send: (message) => {
          this.bridge.postOneWayMessage(NATIVE_BRIDGE_EVENT_NAME.RPC_MESSAGE, message);
        },
        onMessage: (listener) => {
          return this.addMessageListener(WEBVIEW_BRIDGE_EVENT_NAME.RPC_MESSAGE, (data) => {
            listener(data);
          });
        },
      },
    });
  }

  rpcContext: IRPCProtocol;

  get extensionComposer() {
    return this.rpcContext.getProxy(ExtensionContext.ExtensionComposer);
  }

  get extensionWeblogger() {
    return this.rpcContext.getProxy(ExtensionContext.ExtensionWeblogger);
  }

  get extensionConfig() {
    return this.rpcContext.getProxy(ExtensionContext.ExtensionConfig);
  }

  get extensionIndexFile() {
    return this.rpcContext.getProxy(ExtensionContext.ExtensionIndexFile);
  }

  get extensionMCP() {
    return this.rpcContext.getProxy(ExtensionContext.ExtensionMCP);
  }

  get extensionRules() {
    return this.rpcContext.getProxy(ExtensionContext.ExtensionRules);
  }

  get extensionSettings() {
    return this.rpcContext.getProxy(ExtensionContext.ExtensionSettings);
  }

  get logger() {
    return {
      onReportUserAction: (callback: (params: ReportOpt<keyof ReportKeys>) => void) => {
        this.bridge.onMessage(WEBVIEW_BRIDGE_EVENT_NAME.REPORT_USER_ACTION, callback);
      },
    };
  }

  get editor() {
    return {
      openFileToEditor: (filepath: string) => {
        this.bridge.postMessage(NATIVE_BRIDGE_EVENT_NAME.OPEN_FILE_TO_EDITOR_ABSOLUTE_PATH, {
          id: this.bridge.generateId(),
          event: NATIVE_BRIDGE_EVENT_NAME.OPEN_FILE_TO_EDITOR_ABSOLUTE_PATH,
          payload: { filepath },
        });
      },
    };
  }

  // 打印日志
  public printLogger(data: {
    level: "silly" | "debug" | "verbose" | "info" | "warn" | "error";
    msg: string;
    scope: string;
    tags?: LoggerSupplementaryField;
  }) {
    this.bridge.postMessage(NATIVE_BRIDGE_EVENT_NAME.PRINT_LOGGER, {
      id: this.bridge.generateId(),
      event: NATIVE_BRIDGE_EVENT_NAME.PRINT_LOGGER,
      payload: data,
    });
  }

  // 打开URL
  public openUrl(url: string) {
    this.bridge.postMessage(NATIVE_BRIDGE_EVENT_NAME.OPEN_URL, {
      id: this.bridge.generateId(),
      event: NATIVE_BRIDGE_EVENT_NAME.OPEN_URL,
      payload: { url },
    });
  }

  observableAPI: ObservableAPI;

  /**
  * 添加无状态消息监听器，注：可以重复监听哦！
   *
   * Q：为什么要有这么扯淡的多余封装？
   *
   * A：之前的监听方式`this.bridge.onMessage`, 要求只能调用一次，即只能有一个监听函数，多次调用，后面的会把之前的覆盖掉。
   * 估计设计 bridgeAPI，而不是让外部直接调用 bridge.onMessage, 也是出于这样的考虑：将 onMessage 调用收敛到内部，防止多次调用
   *
   * 而新版新增了addMessageListener，可以重复监听，bridgeAPI 意义不大了，但还是先遵循原先的设计架构，暴露统一的出口给外部
   */
  public addMessageListener<T extends WEBVIEW_BRIDGE_EVENT_NAME>(
    event: T,
    listener: (payload: WebviewBridgeParams[T]) => unknown,
  ): () => void {
    return this.bridge.addMessageListener(event, listener);
  }
}

export const kwaiPilotBridgeAPI = new KwaiPilotBridgeAPI();

export interface ArtifactStreamHandler {
  sendData(data: ArtifactPreviewData): void;
  end(error?: unknown): void;
}

function createObservableAPI(messageAPI: KwaiPilotBridge): ObservableAPI {
  return {
    currentFileAndSelection: proxyExtensionAPI(messageAPI, "currentFileAndSelection"),
    visibility: proxyExtensionAPI(messageAPI, "visibility"),
    currentTheme: proxyExtensionAPI(messageAPI, "currentTheme"),
    isDeveloperMode: proxyExtensionAPI(messageAPI, "isDeveloperMode"),
    latestCopiedContent: proxyExtensionAPI(messageAPI, "latestCopiedContent"),
    indexState: proxyExtensionAPI(messageAPI, "indexState"),
    mcpServers: proxyExtensionAPI(messageAPI, "mcpServers"),
    customPanelPage: proxyExtensionAPI(messageAPI, "customPanelPage"),
    rulesList: proxyExtensionAPI(messageAPI, "rulesList"),
    userInfo: proxyExtensionAPI(messageAPI, "userInfo"),
    settingUpdate: proxyExtensionAPI(messageAPI, "settingUpdate"),
  };
}

/**
 * Send a message and return an Observable that will emit the responses.
 */
function callExtensionAPI<T>(messageAPI: KwaiPilotBridge, method: string, args: unknown[]): Observable<T> {
  return new Observable<T>((observer) => {
    const streamId = uuidv4();

    // Stream state
    let finished = false;

    // Set up a listener for the messages in the response stream.
    function messageListener({ streamId: responseStreamId, streamEvent, data }: ResponseMessage): void {
      // If the message is on the stream for this call, emit it.
      if (responseStreamId === streamId) {
        switch (streamEvent) {
          case "next":
            observer.next(data as T);
            break;
          case "error":
            observer.error(data);
            break;
          case "complete":
            finished = true;
            observer.complete();
            break;
        }
      }
    }
    const disposeListener = messageAPI.addMessageListener(
      WEBVIEW_BRIDGE_EVENT_NAME.OBSERVABLE_RESPONSE,
      messageListener,
    );

    messageAPI.postOneWayMessage(NATIVE_BRIDGE_EVENT_NAME.OBSERVABLE_REQUEST, { streamId, method, args });

    return () => {
      disposeListener();
      if (!finished) {
        // Send abort message to peer if the observable is unsubscribed before completion.
        // logRPCMessage("W->X", () => `aborting stream ${streamId}`);
        messageAPI.postOneWayMessage(NATIVE_BRIDGE_EVENT_NAME.OBSERVABLE_REQUEST, {
          streamIdToAbort: streamId,
        });
      }
    };
  });
}
/**
 * Create a proxy for an extension API method.
 */
function proxyExtensionAPI<M extends keyof ObservableAPI>(messageAPI: KwaiPilotBridge, method: M): ObservableAPI[M] {
  return (...args: any[]): Observable<any> => {
    // logRPCMessage("X->W", () => `call method=${method} args=${JSON.stringify(args)}`);
    return callExtensionAPI(messageAPI, method, args);
  };
}
