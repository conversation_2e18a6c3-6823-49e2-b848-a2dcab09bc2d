import { Button } from "@chakra-ui/react";
import { kwaiPilotBridgeAPI } from "../bridge";
import { UserInfo } from "@shared/types";
import { useState, useEffect } from "react";

export const RenderAccount = () => {
  const [userInfo, setUserInfo] = useState<UserInfo | undefined>();

  kwaiPilotBridgeAPI.observableAPI.userInfo().subscribe((userInfo) => {
    setUserInfo(userInfo);
  });

  const logout = () => {
    kwaiPilotBridgeAPI.extensionSettings.$logout();
  };

  const login = () => {
    kwaiPilotBridgeAPI.extensionSettings.$login();
  };

  useEffect(() => {
    const subscription = kwaiPilotBridgeAPI.observableAPI.userInfo().subscribe((userInfo) => {
      setUserInfo(userInfo);
    });
    return () => {
      subscription.unsubscribe();
    };
  }, []);

  return (
    <div className="flex items-center justify-between">
      <div className="flex flex-col text-[var(--vscode-foreground)] text-[13px] leading-[18px] gap-[4px]">
        <div>
          账号设置
        </div>
        <div className="flex align-center text-[13px] leading-[18px] mb-[12px] color-[var(--vscode-button-secondaryForeground)] opacity-50">
          当前登录账号：
          {userInfo?.displayName ?? "未登录"}
        </div>
      </div>
      {userInfo
        ? <Button onClick={() => logout()}>注销</Button>
        : <Button onClick={() => login()}>登录</Button>}
    </div>

  );
};
