import { SettingBlock } from "../components/SettingBlock";
import { PageTitle } from "../components/PageTitle";
import { SettingTitle } from "../components/SettingTitle";
import { SettingDesc } from "../components/SettingDesc";
import { kwaiPilotBridgeAPI } from "@/bridge";
import { Button } from "@chakra-ui/react";

export const Rules = () => {
  const openUserRule = () => {
    kwaiPilotBridgeAPI.extensionRules.$openUserRule();
  };
  const openProjectRules = () => {
    kwaiPilotBridgeAPI.extensionRules.$openProjectRules();
  };
  return (
    <>
      <div className="h-[42px] flex flex-col pl-[15px] mb-[24px]">
        <div className="text-[22px] mr-6 font-medium flex-shrink-0">
          规则配置
        </div>
        <SettingDesc>
          配置可重复使用的、有范围的规则来控制模型的输出方式。
        </SettingDesc>
      </div>
      <SettingBlock className="flex items-center justify-between  gap-4">
        <div>
          <SettingTitle>个人规则</SettingTitle>
          <SettingDesc>
            在此文件中配置用户习惯后，Kwaipilot在问答模式及智能体模式的所有对话场景中均遵循设定规则，且跨项目切换时持续生效。
          </SettingDesc>
        </div>
        <Button onClick={openUserRule}>打开</Button>
      </SettingBlock>
      <SettingBlock className="flex items-center justify-between gap-4">
        <div>
          <SettingTitle>项目规则</SettingTitle>
          <SettingDesc>
            配置项目内使用的规则，在当前项目的问答模式与智能体模式的会话中生效，可在当前工作区的.kwaipilot/rules文件夹查看当前项目内的所有规则。
          </SettingDesc>
        </div>
        <Button onClick={openProjectRules}>添加</Button>
      </SettingBlock>
    </>
  );
};
