import { inputAnatomy } from "@chakra-ui/anatomy";
import { createMultiStyleConfigHelpers } from "@chakra-ui/react";

const { defineMultiStyleConfig, definePartsStyle } = createMultiStyleConfigHelpers(inputAnatomy.keys);

export default defineMultiStyleConfig({
  sizes: {
    sm: definePartsStyle({
      field: {
        height: "32px",
        padding: "0 8px",
        fontSize: "13px",
        lineHeight: "18px",
        borderRadius: "4px",
      },
    }),
  },
  variants: {
    outline: definePartsStyle({
      field: {
        color: "var(--vscode-foreground)",
        border: "1px solid",
        borderColor: "var(--vscode-dropdown-border)",
        _hover: {
          borderColor: "var(--vscode-focusBorder)",
          outline: "none",
        },
        _focus: {
          borderColor: "var(--vscode-focusBorder)",
          outline: "none",
          boxShadow: "none", // 新增，去除聚焦时的阴影
        },
      },
    }),
  },
  defaultProps: { size: "sm", variant: "outline" },
});
