// 用于控制直接启动 kwaipilot-binary 进程
process.env.NODE_ENV = "production";

import { ExtensionContext } from "vscode";
import * as vscode from "vscode";
import { v4 as uuidv4 } from "uuid";
import { ContextManager } from "./base/context-manager";
import { Ast } from "./core/ast";
import { AnnotationTagService } from "./services/annotation-tag";
import { WebloggerManager } from "./base/weblogger";
import { LoginService } from "./services/login";
import { BrowserService } from "./services/browser";
import { QuickAskService } from "./services/quick-ask";
import { CodeActionService } from "./services/code-action";
import { LoggerManager } from "./base/logger";
import { GlobalStateManager, WorkspaceStateManager, ConfigManager } from "./base/state-manager";
import { GlobalState } from "./base/state-manager/types";
import { Project } from "./core/project";
import { File } from "./core/file";
import { Api } from "./base/http-client";
import { DefaultBaseUrl, KwaipilotEnv } from "./const";
import { DocumentManager } from "./core/document";
import { CompletionModule } from "./core/completion";
import { InlineChatService } from "./services/inline-chat";
import { DiffModule } from "./core/diff";
import { SqlLite } from "./services/sql-lite";
import { PredictionService } from "./services/prediction";
import { Bridge } from "@bridge";
import { Webview } from "@webview";
import { AgentModule } from "./core/agent";
import { SettingPanelModule } from "./core/setting-panel";
import { WriteToFileService } from "./services/write-to-file";
import { ComposerService } from "./services/composer";
import { ComposerSessionStorageService } from "./services/composer/ComposerSessionStorageService";
import { ApiConversationHistoryStorageService } from "./services/composer/ApiConversationHistoryStorageService";
import { LocalService } from "./core/localService";
import { ComposerHistoryStorageService } from "./services/composer/ComposerHistoryStorageService";
import { VSCodeNativeBridgeRegistry } from "./services/bridge-registry/VSCodeNativeBridgeRegistry";
import { IndexFileService } from "./services/index-file";
import { MCPService } from "./services/mcp";
import { RulesService } from "./services/rules";
import { DeveloperService } from "./services/developer";
import { ToLocalService } from "./services/to-loacl-message";
import { ThemeService } from "./services/theme";

KwaipilotEnv.setIsInIde(true);

const activateAsync = async (context: ExtensionContext) => {
  // #region 注册基础设施
  const ctx = new ContextManager(context);
  const globalState = new GlobalStateManager(ctx);
  ctx.registryBase(GlobalStateManager, globalState);

  const logger = new LoggerManager(ctx);
  ctx.registryBase(LoggerManager, logger);

  const workspaceState = new WorkspaceStateManager(ctx);
  ctx.registryBase(WorkspaceStateManager, workspaceState);

  const config = new ConfigManager(ctx);
  ctx.registryBase(ConfigManager, config);

  // 替换为新的桥接类
  const bridge = new Bridge(ctx);
  ctx.registryBase(Bridge, bridge);

  const weblogger = new WebloggerManager(ctx);
  ctx.registryBase(WebloggerManager, weblogger);

  ctx.registryBase(Api, new Api(ctx, { defaultBaseUrl: DefaultBaseUrl }));

  const webview = new Webview(ctx);
  ctx.registryBase(Webview, webview);

  // #endregion

  // #region 注册核心服务
  ctx.registryCore(Ast, new Ast(ctx));
  ctx.registryCore(Project, new Project(ctx));
  ctx.registryCore(File, new File(ctx));
  ctx.registryCore(DocumentManager, new DocumentManager(ctx));
  ctx.registryCore(CompletionModule, new CompletionModule(ctx));
  ctx.registryCore(DiffModule, new DiffModule(ctx));
  ctx.registryCore(SettingPanelModule, new SettingPanelModule(ctx));
  ctx.registryCore(LocalService, new LocalService(ctx));
  ctx.registryCore(AgentModule, new AgentModule(ctx));
  // #endregion

  // #region 注册业务域
  ctx.registryService(LoginService, new LoginService(ctx));
  ctx.registryService(BrowserService, new BrowserService(ctx));

  const quickAsk = new QuickAskService(ctx);
  ctx.registryService(QuickAskService, quickAsk);

  const codeAction = new CodeActionService(ctx);
  ctx.registryService(CodeActionService, codeAction);

  // inline-chat
  ctx.registryService(InlineChatService, new InlineChatService(ctx));

  // activateCommentTag(context, chatProvider);
  ctx.registryService(AnnotationTagService, new AnnotationTagService(ctx));

  ctx.registryService(SqlLite, new SqlLite(ctx));

  ctx.registryService(PredictionService, new PredictionService(ctx));
  ctx.registryService(WriteToFileService, new WriteToFileService(ctx));

  ctx.registryService(ComposerSessionStorageService, new ComposerSessionStorageService(ctx));
  ctx.registryService(ComposerHistoryStorageService, new ComposerHistoryStorageService(ctx));
  ctx.registryService(ApiConversationHistoryStorageService, new ApiConversationHistoryStorageService(ctx));

  ctx.registryService(ComposerService, new ComposerService(ctx));

  ctx.registryService(ThemeService, new ThemeService(ctx));
  ctx.registryService(IndexFileService, new IndexFileService(ctx));
  ctx.registryService(MCPService, new MCPService(ctx));
  ctx.registryService(RulesService, new RulesService(ctx));
  ctx.registryService(DeveloperService, new DeveloperService(ctx));
  ctx.registryService(ToLocalService, new ToLocalService(ctx));

  ctx.registryService(VSCodeNativeBridgeRegistry, new VSCodeNativeBridgeRegistry(ctx));
  // #endregion
};
export function activate(context: ExtensionContext) {
  if (context.extensionMode === vscode.ExtensionMode.Development) {
    // @ts-expect-error for testing
    globalThis._vscode = vscode;
    // @ts-expect-error for testing
    globalThis._context = context;
  }
  let deviceId: string | undefined = context.globalState.get("kwaipilot.deviceId");
  if (!deviceId) {
    deviceId = uuidv4();
    context.globalState.update("kwaipilot.deviceId", deviceId);
  }

  vscode.commands.registerCommand("kwaipilot.bridge.userInfo", (userInfo: any) => {
    context.globalState.update(GlobalState.USER_INFO, userInfo);
  });

  // 先启动异步激活过程
  activateAsync(context).then(() => {
    console.log("[Kwaipilot] Extension activation extension-export");
    vscode.commands.executeCommand("kwaipilot.bridge.extensionHostReady");
  });
}

export function deactivate() { }
