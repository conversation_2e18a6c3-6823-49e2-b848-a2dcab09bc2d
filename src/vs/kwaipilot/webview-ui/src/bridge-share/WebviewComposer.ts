import { WebviewComposerShape } from "shared/lib/bridge/protocol";
import { ComposerState } from "shared/lib/agent";
import { MentionNodeV2Structure } from "shared/lib/MentionNodeV2/nodes";
import { useComposerMessageConsumer } from "@/store/composerMessageConsumer";

export class WebviewComposer implements WebviewComposerShape {
  $postComposerStateUpdate(_state: ComposerState): void {
    throw new Error("Method not implemented.");
  }

  async $addToComposerContext(node: MentionNodeV2Structure | null): Promise<void> {
    const { router } = await import("@/router");
    if (!router.state.location.pathname.startsWith("/composer-v2")) {
      router.navigate("/composer-v2");
    }
    if (node) {
      useComposerMessageConsumer.getState().produceComposerContext(node);
    }
  }
}
