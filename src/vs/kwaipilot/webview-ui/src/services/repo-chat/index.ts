import { kwaiPilotBridgeAPI } from "@/bridge";
import { ext2LanguageId } from "@/constant";
import { httpClient } from "@/http";
import {
  CodeSearchCheckoutRequest,
  GeneratePromptFile,
  TreeNode,
} from "@/http/interface";
import { getUserInfo } from "@/utils/getUserInfo";
import { logger } from "@/utils/logger";

const maxLen = 20;

/**
 * TODO: 和 vscode 插件部分的 enum 公用
 */
export enum WorkspaceState {
  /** 选择过的文件 */
  CODE_SEARCH_SELECT_FILE_HISTORY = "codeSearchSelectFileHistory",
  /** 选择过的目录目录 */
  CODE_SEARCH_SELECT_DIR_HISTORY = "codeSearchSelectDirHistory",
  /** 打开的文件 and 打开过的文件 */
  CODE_SEARCH_SELECT_OPEN_FILE_HISTORY = "codeSearchSelectOpenFileHistory",
  /** 打开文件的目录 and 打开过文件的目录 */
  CODE_SEARCH_SELECT_OPEN_DIR_HISTORY = "codeSearchSelectOpenDirHistory",
  /** 当前工作区激活的会话 */
  ACTIVE_SESSION_ID = "activeSessionId",
  ACTIVE_COMPOSER_SESSION_ID = "activeComposerSessionId",
}

type CodeSearchSelectKey =
  | WorkspaceState.CODE_SEARCH_SELECT_DIR_HISTORY
  | WorkspaceState.CODE_SEARCH_SELECT_FILE_HISTORY
  | WorkspaceState.CODE_SEARCH_SELECT_OPEN_FILE_HISTORY
  | WorkspaceState.CODE_SEARCH_SELECT_OPEN_DIR_HISTORY
  | WorkspaceState.CODE_SEARCH_SELECT_DIR_HISTORY;

enum PathType {
  FILE = "file",
  DIR = "dir",
}

class RepoChatService {
  private loggerScope = "repo-chat";
  private codeSearchController: AbortController | null;
  private remoteOriginUrl?: string;
  private workspacePath?: string;
  private isGitRepo = false;
  private activeFilePath?: string;
  private initPromise: Promise<void>;

  constructor() {
    this.codeSearchController = null;
    this.initPromise = this.initialize();
  }

  private async initialize() {
    await Promise.all([
      this.setCodeSearchSelectOpenFileAndFoldHistory(),
      kwaiPilotBridgeAPI.getActiveEditor().then((editor) => {
        this.currentFilePath = editor.document.relativePath;
      }),
      this.initCodeSearch(),
    ]);
  }

  async setCodeSearchSelectOpenFileAndFoldHistory() {
    const { list: files } = await kwaiPilotBridgeAPI.getOpenTabFiles();
    if (!files) {
      return;
    }
    logger.info("update open file and dir", this.loggerScope);

    this.setSelectFileQueue(
      WorkspaceState.CODE_SEARCH_SELECT_OPEN_FILE_HISTORY,
      files,
    );
    this.setSelectFileQueue(
      WorkspaceState.CODE_SEARCH_SELECT_OPEN_DIR_HISTORY,
      files.map(this.handleFilePathToDir),
    );
  }

  /** 开始构建索引 */
  async startIndexBuild(params: CodeSearchCheckoutRequest) {
    if (this.codeSearchController) {
      this.codeSearchController.abort();
    }

    const newController = new AbortController();
    this.codeSearchController = newController;
    try {
      await this.indexBuild(params, newController.signal);
    }
    catch (error) {
      logger.error("index build error", this.loggerScope, {
        reason: "index build error",
        err: error,
      });
    }
  }

  /** 初始化构建索引 */
  async initCodeSearch() {
    const userInfo = await getUserInfo();

    if (userInfo) {
      // NOTE 需要一个 bridge 获取当前所在目录
      const repoPath = await this.getRepoPath();
      const { result: inRepo } = (await kwaiPilotBridgeAPI.executeCmd(
        `git -C ${repoPath} rev-parse --is-inside-work-tree`,
      )) as { result: string };

      if (inRepo === "true") {
        this.isGitRepo = true;
        const { result: remoteOriginUrl }
          = (await kwaiPilotBridgeAPI.executeCmd(
            `git -C ${repoPath} config --get remote.origin.url`,
          )) as { result: string };
        this.remoteOriginUrl = remoteOriginUrl;
        const isKwaiGitRepo
          = remoteOriginUrl?.startsWith("*************************")
          || remoteOriginUrl?.startsWith("https://git.corp.kuaishou.com")
          || false;

        if (isKwaiGitRepo) {
          this.startIndexBuild({
            repoName: this.handleGitUrl(remoteOriginUrl),
            branch: await this.getBranch(),
            commit: await this.getCommit(),
            username: userInfo.name,
          });
        }
        else {
          logger.info("init code search error", this.loggerScope, {
            reason: "repo is not kwai repo",
          });
        }
      }
      else {
        logger.info("init code search error", this.loggerScope, {
          reason: "repoPath is null",
        });
      }
    }
    else {
      logger.info("init code search error", this.loggerScope, {
        reason: "userInfo is null",
      });
    }
  }

  async indexBuild(params: CodeSearchCheckoutRequest, signal: AbortSignal) {
    let buildStatus = (await httpClient.getCodeSearchCheckout(params, signal))
      .status;

    if (buildStatus === -1) {
      logger.info("index build", this.loggerScope, {
        reason: "this repo not support code search",
        value: params.repoName,
      });
      return;
    }

    buildStatus = (await httpClient.getCodeSearchCheckout(params, signal))
      .status;
  }

  /** 获取指令选中的文件内容 */
  async getSelectFileCode(filepaths: string[]) {
    const res: GeneratePromptFile[] = [];
    for (const f of filepaths) {
      try {
        const { content } = await kwaiPilotBridgeAPI.fs.readFile(
          this.getAbsolutePath(f),
        );
        res.push({
          code: content,
          language: ext2LanguageId[f.split(".").pop() ?? ""] ?? "",
          name: f,
        });
      }
      catch (err: any) {
        logger.error("get select file code error", this.loggerScope, {
          reason: "get select file code error",
          err: err,
        });
      }
    }
    return res;
  }

  /** 获取代码搜索默认展示的文件 */
  private async getCodeSearchDefaultFiles() {
    const queue = [
      ...(await this.getSelectFileQueue(
        WorkspaceState.CODE_SEARCH_SELECT_OPEN_FILE_HISTORY,
      )),
      ...(await this.getSelectFileQueue(
        WorkspaceState.CODE_SEARCH_SELECT_FILE_HISTORY,
      )),
    ];
    const res = Array.from(new Set(queue.slice(0, maxLen)));
    logger.info("get code search default files", this.loggerScope, {
      value: res,
    });
    return res;
  }

  /** 获取代码搜索默认展示的目录 */
  private async getCodeSearchDefaultDirs() {
    const queue = [
      ...(await this.getSelectFileQueue(
        WorkspaceState.CODE_SEARCH_SELECT_OPEN_DIR_HISTORY,
      )),
      ...(await this.getSelectFileQueue(
        WorkspaceState.CODE_SEARCH_SELECT_DIR_HISTORY,
      )),
    ];
    const res = Array.from(new Set(queue.slice(0, maxLen)));
    logger.info("get code search default dirs", this.loggerScope, {
      value: res,
    });
    return res;
  }

  /** 获取工作区默认文件列表和目录列表 */
  async getWorkspaceFileListAndDirList() {
    return {
      files: await this.getCodeSearchDefaultFiles(),
      dirs: await this.getCodeSearchDefaultDirs(),
    };
  }

  /** 获取选择文件队列 */
  private async getSelectFileQueue(key: CodeSearchSelectKey) {
    const pathType = this.getPathType(key);

    const queue = Array.from(
      new Set((await kwaiPilotBridgeAPI.getState<string[]>(key)).value),
    );

    const newQueue = queue.filter(async (dir) => {
      try {
        const { status } = await kwaiPilotBridgeAPI.getFileStatus(
          this.getAbsolutePath(dir),
        );
        if (pathType === PathType.DIR) {
          return status.isDirectory();
        }
        else if (pathType === PathType.FILE) {
          return status.isFile();
        }
        return false;
      }
      catch (error) {
        return false;
      }
    });
    if (newQueue.length !== queue.length) {
      kwaiPilotBridgeAPI.updateState(key, newQueue);
    }
    return newQueue;
  }

  /** 获取路径类型 */
  private getPathType(key: CodeSearchSelectKey): PathType {
    switch (key) {
      case WorkspaceState.CODE_SEARCH_SELECT_DIR_HISTORY:
      case WorkspaceState.CODE_SEARCH_SELECT_OPEN_DIR_HISTORY:
        return PathType.DIR;
      case WorkspaceState.CODE_SEARCH_SELECT_FILE_HISTORY:
      case WorkspaceState.CODE_SEARCH_SELECT_OPEN_FILE_HISTORY:
        return PathType.FILE;
    }
  }

  /** 获取工作区文件列表 */
  async getWorkspaceFileList(
    currentGitInfo: {
      excludeDirList?: string[];
    } = {
        excludeDirList: ["node_modules"],
      },
  ) {
    const repoPath = await this.getRepoPath();

    // 如果是git仓库，使用git命令获取文件列表
    if (this.isGitRepo) {
      let exclude = "";
      if (currentGitInfo.excludeDirList?.length) {
        currentGitInfo.excludeDirList.forEach((dir) => {
          exclude += ` --exclude='${dir}' `;
        });
      }

      const { result: filesString } = (await kwaiPilotBridgeAPI.executeCmd(
        `git -C ${repoPath} ls-files --cached --others ${exclude} ${this.workspacePath || ""
        }`,
      )) as { result: string };

      return filesString.split("\n").filter(Boolean);
    }

    // 非git仓库，使用文件系统API获取文件列表
    const { files } = await kwaiPilotBridgeAPI.fs.readDirectory(repoPath, {
      recursive: true,
      excludes: currentGitInfo.excludeDirList || [],
    });

    // 转换为相对路径
    return files;
  }

  /** 获取工作区tree */
  async getWorkspaceTree() {
    const files = await this.getWorkspaceFileList();
    return this.convertToTree(files);
  }

  /** 获取工作区目录 */
  async getWorkspaceDirList() {
    if (!this.isGitRepo) {
      return [];
    }
    const repoPath = await this.getRepoPath();
    const workspacePath = this.workspacePath;
    if (!repoPath || !workspacePath) {
      return [];
    }
    const currentBranch = await this.getBranch();
    const hasInfo = currentBranch || workspacePath;
    /**
     * TODO: 可以强制忽略一些目录啊
     */
    let extraInfo = "";

    if (hasInfo) {
      extraInfo += `${currentBranch || "HEAD"}`;
    }
    const { result: dirListString } = (await kwaiPilotBridgeAPI.executeCmd(
      `git -C ${repoPath} ls-tree -d -r --name-only ${extraInfo}`,
    )) as { result: string };
    const { result: notManageByGitFilesString }
      = (await kwaiPilotBridgeAPI.executeCmd(
        `git -C ${repoPath} ls-files --others --exclude-standard ${workspacePath}`,
      )) as { result: string };

    const manageByGitDir = dirListString.split("\n");
    const notManageByGitFiles = notManageByGitFilesString.split("\n");
    const notManageByGitDir = notManageByGitFiles.map(file =>
      file.split("/").slice(0, -1).join("/"),
    );

    const filterArray = Array.from(
      new Set([...manageByGitDir, ...notManageByGitDir]),
    );
    return filterArray.sort();
  }

  async getCurrentFilePathAndRepoPath() {
    await this.initPromise;
    const repoPath = await this.getRepoPath();

    return {
      filePath: this.activeFilePath,
      repoPath,
    };
  }

  async getWorkspacePathAndRepoPath() {
    return {
      workspacePath: this.workspacePath,
      repoPath: await this.getRepoPath(),
    };
  }

  async setSelectFileQueue(key: CodeSearchSelectKey, path: string[]) {
    const queue
      = (await kwaiPilotBridgeAPI.getState<string[]>(key)).value ?? [];
    const workspacePath = await this.getRepoPath();

    const relativePaths = path.map(p =>
      p.startsWith(workspacePath) ? p.slice(workspacePath.length) : p,
    );
    const newQueue = Array.from(new Set([...relativePaths, ...queue]));

    kwaiPilotBridgeAPI.updateState(key, newQueue.slice(0, maxLen));
  }

  /** 更新代码搜索选择的历史 */
  updateCodeSearchSelectHistory(data: { file: string[]; dir: string[] }) {
    const { file, dir } = data;
    if (file.length) {
      this.setSelectFileQueue(
        WorkspaceState.CODE_SEARCH_SELECT_FILE_HISTORY,
        file,
      );
    }

    if (dir.length) {
      this.setSelectFileQueue(
        WorkspaceState.CODE_SEARCH_SELECT_DIR_HISTORY,
        dir,
      );
    }
  }

  /** 处理 git url */
  private handleGitUrl(gitUrl: string) {
    const prefix = gitUrl.startsWith("https://")
      ? "https://git.corp.kuaishou.com/"
      : "*************************:";
    const suffix = ".git";
    let str = gitUrl.startsWith(prefix) ? gitUrl.slice(prefix.length) : gitUrl;
    str = str.endsWith(suffix) ? str.slice(0, -suffix.length) : str;
    return str;
  }

  private convertToTree(files: string[]): TreeNode[] {
    const root: TreeNode[] = [];

    for (const file of files) {
      const parts = file.split("/");
      let currentLevel = root;

      for (let i = 0; i < parts.length; i++) {
        const part = parts[i];
        const isLastPart = i === parts.length - 1;

        let node = currentLevel.find(n => n.name === part);

        if (!node) {
          node = {
            name: part,
            children: [],
            type: isLastPart ? 0 : 1, // 如果是最后一部分则为文件(0)，否则为目录(1)
          };
          currentLevel.push(node);
        }

        currentLevel = node.children;
      }
    }

    return root;
  }

  public handleFilePathToDir(filePath: string) {
    return filePath.split("/").slice(0, -1).join("/");
  }

  get repoName() {
    return this.handleGitUrl(this.remoteOriginUrl ?? "");
  }

  get remoteUrl() {
    return this.remoteOriginUrl;
  }

  get currentFilePath() {
    return this.activeFilePath || "";
  }

  set currentFilePath(filePath: string) {
    this.activeFilePath = filePath;
  }

  async getCommit() {
    const repoPath = await this.getRepoPath();
    const { result: commit } = (await kwaiPilotBridgeAPI.executeCmd(
      `git -C ${repoPath} rev-parse HEAD`,
    )) as { result: string };
    return commit;
  }

  async getBranch() {
    try {
      const repoPath = await this.getRepoPath();
      const { result: branch } = (await kwaiPilotBridgeAPI.executeCmd(
        `git -C ${repoPath} rev-parse --abbrev-ref HEAD`,
      )) as { result: string };

      console.log("getBranch", branch);
      return branch;
    }
    catch (e) {
      return "";
    }
  }

  async getRepoPath() {
    const { result: workspaceUri }
      = (await kwaiPilotBridgeAPI.getWorkspaceUri()) as {
        result: string;
      };
    if (!workspaceUri) {
      this.workspacePath = "";
      return "";
    }
    if (workspaceUri.startsWith("file://")) {
      this.workspacePath = workspaceUri.slice(7);
    }
    else {
      this.workspacePath = workspaceUri;
    }
    return this.workspacePath;
  }

  getAbsolutePath(filePath: string) {
    if (!this.workspacePath) {
      logger.error("workspacePath is null", this.loggerScope);
      return filePath;
    }
    const repoPath = this.workspacePath;
    const repoPahtWithSlash = repoPath.endsWith("/")
      ? repoPath
      : repoPath + "/";
    const filePathWithNoSlash = filePath.startsWith("/")
      ? filePath.slice(1)
      : filePath;

    return repoPahtWithSlash + filePathWithNoSlash;
  }
}
const repoChatService = new RepoChatService();
export default repoChatService;
