import { app, ipcMain } from 'electron';
import { ConfigManager } from './vs/welcome/electron-main/config/configManager.js';
import { WelcomeWindow } from './vs/welcome/electron-main/window/welcomeWindow.js';
import { checkFirstTimeStartup, markFirstTimeCompleted } from './vs/welcome/electron-main/startup/firstTimeStartup.js';
import { product } from './bootstrap-meta.js';
import { ShellCommandInstaller } from './vs/welcome/electron-main/utils/installCmd.js';

const isDev = process.env.NODE_ENV === 'development';

const themeIdMap = {
	'Dark-Modern': 'Default Dark Modern',
	'Light-Modern': 'Default Light Modern',
	'Tomorrow-Night-Blue': 'Tomorrow Night Blue',
};

export { checkFirstTimeStartup, markFirstTimeCompleted };

export async function showWelcomeWindow(): Promise<void> {
	const welcomeWindow = new WelcomeWindow(isDev);
	const window = await welcomeWindow.show();
	const nameShort = product.nameShort ?? 'code-oss-dev';
	const configManager = new ConfigManager();

	// 监听欢迎流程完成事件
	window.webContents.on('ipc-message', async (event, channel, data) => {
		// 流程结束
		if (channel === 'welcome-completed') {
			await markFirstTimeCompleted();
			window.close();
			app.relaunch();
			app.exit();
		}
		// set-theme
		if (channel === 'set-theme') {
			// @ts-ignore
			await configManager.setTheme(themeIdMap[data]);
		}
	});

	// 导入配置
	ipcMain.handle('import-config', async (event, nameShort: 'Code' | 'Cursor') => {
		try {
			return await configManager.importConfig(nameShort);
		} catch (error) {
			console.error('Import failed:', error);
			return {
				status: 'error',
				message: error.message,
			};
		}
	});

	// 安装命令
	ipcMain.handle('install-cli', async (event) => {
		try {
			const installer = new ShellCommandInstaller(nameShort);
			// 安装命令
			await installer.installShellCommand();
			return {
				status: 'success',
				message: 'Installation successful',
			};
		} catch (error) {
			console.error('Install failed:', error);
			return {
				status: 'error',
				message: error.message,
			};
		}
	});

	// 登录逻辑
	ipcMain.handle('login', async (event) => {
		try {
			const { LoginManager } = await import('./vs/welcome/electron-main/login/loginManager.js');
			const loginManager = new LoginManager(configManager.getStorageService());

			const result = await loginManager.show();

			if (result.status === 'success' && result.userInfo) {
				return { isLoggedIn: true, userInfo: result.userInfo };
			}

			throw new Error(result.message);
		} catch (error) {
			console.error('Login failed:', error);
			return { isLoggedIn: false, message: error.message };
		}
	});

}
