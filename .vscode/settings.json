{
	"editor.insertSpaces": false,
	"files.trimTrailingWhitespace": true,
	"files.watcherExclude": {
		"**/local-agent/**": true // 排除二进制目录
	},
	"files.exclude": {
		".git": true,
		".profile-oss": true,
		"**/.DS_Store": true,
		".vscode-test": true,
		"cli/target": true,
		"build/**/*.js.map": true,
		"**/local-agent/kwaipilot-binary": true, // 排除二进制目录
		"**/local-agent/*.node": true // 排除二进制目录
	},
	"files.associations": {
		"cglicenses.json": "jsonc",
		"*.tst": "typescript"
	},
	"search.exclude": {
		"**/node_modules": true,
		"cli/target/**": true,
		".build/**": true,
		"out/**": true,
		"out-build/**": true,
		"out-vscode/**": true,
		"i18n/**": true,
		"extensions/**/dist/**": true,
		"extensions/**/out/**": true,
		"test/smoke/out/**": true,
		"test/automation/out/**": true,
		"test/integration/browser/out/**": true,
		"src/vs/base/test/common/filters.perf.data.js": true,
		"src/vs/base/test/node/uri.perf.data.txt": true,
		"src/vs/workbench/api/test/browser/extHostDocumentData.test.perf-data.ts": true,
		"src/vs/base/test/node/uri.test.data.txt": true,
		"src/vs/editor/test/node/diffing/fixtures/**": true,
		"build/loader.min": true,
		"**/local-agent/**": true // 排除二进制目录
	},
	"files.readonlyInclude": {
		"**/node_modules/**/*.*": true,
		"**/yarn.lock": true,
		"**/package-lock.json": true,
		"**/Cargo.lock": true,
		"out/**": true,
		"out-build/**": true,
		"out-vscode/**": true,
		"out-vscode-reh/**": true,
		"extensions/**/dist/**": true,
		"extensions/**/out/**": true,
		"extensions/terminal-suggest/src/completions/upstream/**": true,
		"test/smoke/out/**": true,
		"test/automation/out/**": true,
		"test/integration/browser/out/**": true
	},
	"files.readonlyExclude": {
		"build/builtin/*.js": true,
		"build/monaco/*.js": true,
		"build/npm/*.js": true,
		"build/*.js": true
	},
	"lcov.path": [
		"./.build/coverage/lcov.info",
		"./.build/coverage-single/lcov.info"
	],
	"lcov.watch": [
		{
			"pattern": "**/*.test.js",
			"command": "${workspaceFolder}/scripts/test.sh --coverage --run ${file}",
			"windows": {
				"command": "${workspaceFolder}\\scripts\\test.bat --coverage --run ${file}"
			}
		}
	],
	"typescript.tsdk": "node_modules/typescript/lib",
	"npm.exclude": "**/extensions/**",
	"emmet.excludeLanguages": [],
	"typescript.preferences.importModuleSpecifier": "relative",
	"typescript.preferences.quoteStyle": "single",
	"json.schemas": [
		{
			"fileMatch": ["cgmanifest.json"],
			"url": "https://json.schemastore.org/component-detection-manifest.json"
		},
		{
			"fileMatch": ["cglicenses.json"],
			"url": "./.vscode/cglicenses.schema.json"
		}
	],
	"git.ignoreLimitWarning": true,
	"git.branchProtection": ["main", "distro", "release/*"],
	"git.branchProtectionPrompt": "alwaysCommitToNewBranch",
	"git.branchRandomName.enable": true,
	"git.pullBeforeCheckout": true,
	"git.mergeEditor": true,
	"remote.extensionKind": {
		"msjsdiag.debugger-for-chrome": "workspace"
	},
	"gulp.autoDetect": "off",
	"files.insertFinalNewline": true,
	"[plaintext]": {
		"files.insertFinalNewline": false
	},
	"[typescript]": {
		"editor.defaultFormatter": "vscode.typescript-language-features",
		"editor.formatOnSave": true
	},
	"[javascript]": {
		"editor.defaultFormatter": "vscode.typescript-language-features",
		"editor.formatOnSave": true
	},
	"[rust]": {
		"editor.defaultFormatter": "rust-lang.rust-analyzer",
		"editor.formatOnSave": true
	},
	"rust-analyzer.linkedProjects": ["cli/Cargo.toml"],
	"typescript.tsc.autoDetect": "off",
	"testing.autoRun.mode": "rerun",
	"conventionalCommits.scopes": [
		"tree",
		"scm",
		"grid",
		"splitview",
		"table",
		"list",
		"git",
		"sash"
	],
	"githubPullRequests.experimental.createView": true,
	"debug.javascript.terminalOptions": {
		"outFiles": [
			"${workspaceFolder}/out/**/*.js",
			"${workspaceFolder}/build/**/*.js"
		]
	},
	"extension-test-runner.debugOptions": {
		"outFiles": ["${workspaceFolder}/extensions/*/out/**/*.js"]
	},
	"githubPullRequests.assignCreated": "${user}",
	"githubPullRequests.defaultMergeMethod": "squash",
	"githubPullRequests.ignoredPullRequestBranches": ["main"],
	"application.experimental.rendererProfiling": true,
	"editor.experimental.asyncTokenization": true,
	"editor.experimental.asyncTokenizationVerification": true,
	"terminal.integrated.suggest.enabled": true,
	"typescript.preferences.autoImportFileExcludePatterns": [
		"@xterm/xterm",
		"@xterm/headless",
		"node-pty",
		"vscode-notebook-renderer",
		"src/vs/workbench/workbench.web.main.internal.ts"
	],
	"[github-issues]": {
		"editor.wordWrap": "on"
	},
	"css.format.spaceAroundSelectorSeparator": true,
	"typescript.enablePromptUseWorkspaceTsdk": true,
	"eslint.useFlatConfig": true,
	"editor.occurrencesHighlightDelay": 0,
	"typescript.experimental.expandableHover": true,
	"git.diagnosticsCommitHook.Enabled": true,
	"git.diagnosticsCommitHook.Sources": {
		"*": "error",
		"ts": "warning",
		"eslint": "warning"
	},
	"prettier.enable": false,
	"github.copilot.chat.commitMessageGeneration.instructions": [
		{
			"text": "请使用中文生成 Angular 规范的提交信息，格式为：type(scope): subject。其中 type 必须是以下类型之一：feat (新功能), fix (修复), docs (文档), style (格式), refactor (重构), perf (性能优化), test (测试), chore (构建/工具), ci (持续集成)。scope 是可选的，表示影响范围。subject 是简短的描述，不超过 50 个字符。示例：feat(用户模块): 添加用户登录功能"
		}
  	]
}
